# Examples CMakeLists.txt

# UDS Echo Server Example
add_executable(uds_echo_server uds_echo_server.cpp)

target_link_libraries(uds_echo_server
    PRIVATE
        zexuan_net
        zexuan_base
)

target_include_directories(uds_echo_server
    PRIVATE
        ${CMAKE_SOURCE_DIR}/include
)

# Set output directory for examples
set_target_properties(uds_echo_server
    PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin/examples
)

# UDS Echo Client Example
add_executable(uds_echo_client uds_echo_client.cpp)

target_link_libraries(uds_echo_client
    PRIVATE
        zexuan_net
        zexuan_base
)

target_include_directories(uds_echo_client
    PRIVATE
        ${CMAKE_SOURCE_DIR}/include
)

# Set output directory for examples
set_target_properties(uds_echo_client
    PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin/examples
)

# Install examples (optional)
install(TARGETS uds_echo_server uds_echo_client
    RUNTIME DESTINATION bin/examples
    COMPONENT examples
)
