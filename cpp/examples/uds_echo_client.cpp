#include <iostream>
#include <unistd.h>
#include <chrono>

#include "zexuan/net/event_loop.hpp"
#include "zexuan/net/uds_client.hpp"

using namespace zexuan;
using namespace zexuan::net;

class EchoClient {
public:
    EchoClient(EventLoop* loop, const std::string& serverPath)
        : client_(loop, serverPath, "EchoClient"),
          messageCount_(0),
          maxMessages_(5),
          connected_(false) {
        client_.setConnectionCallback([this](const UdsConnectionPtr& conn) {
            onConnection(conn);
        });
        client_.setMessageCallback([this](const UdsConnectionPtr& conn, Buffer* buf, Timestamp time) {
            onMessage(conn, buf, time);
        });
        // Don't enable retry to avoid infinite reconnection
        // client_.enableRetry();
    }

    void connect() {
        client_.connect();
    }

    void disconnect() {
        client_.disconnect();
    }

private:
    void onConnection(const UdsConnectionPtr& conn) {
        std::cout << "EchoClient - client[fd=" << conn->fd() << "] -> "
                  << conn->peerPath() << " is "
                  << (conn->connected() ? "UP" : "DOWN") << std::endl;

        if (conn->connected()) {
            connected_ = true;
            messageCount_ = 0;  // Reset message count on new connection
            // Send the first message
            sendMessage(conn);
        } else {
            connected_ = false;
        }
    }

    void onMessage(const UdsConnectionPtr& conn, Buffer* buf, Timestamp time) {
        std::string msg(buf->retrieveAllAsString());
        std::cout << "EchoClient - received echo: \"" << msg << "\" at "
                  << std::chrono::duration_cast<std::chrono::milliseconds>(
                       time.time_since_epoch()).count() << std::endl;

        messageCount_++;

        // Check if we've sent enough messages
        if (messageCount_ < maxMessages_) {
            // Send next message immediately (no blocking sleep)
            sendMessage(conn);
        } else {
            std::cout << "EchoClient - Sent " << messageCount_ << " messages. Disconnecting..." << std::endl;
            conn->shutdown();
        }
    }

    void sendMessage(const UdsConnectionPtr& conn) {
        std::string message = "Hello from UDS client #" + std::to_string(messageCount_ + 1);
        std::cout << "EchoClient - sending: \"" << message << "\"" << std::endl;
        conn->send(message);
    }

    UdsClient client_;
    int messageCount_;
    int maxMessages_;
    bool connected_;
};

int main(int argc, char* argv[]) {
    std::cout << "pid = " << getpid() << std::endl;

    std::string serverPath = "/tmp/echo_server.sock";
    if (argc > 1) {
        serverPath = argv[1];
    }
    
    std::cout << "UDS Echo Client connecting to: " << serverPath << std::endl;
    
    EventLoop loop;
    EchoClient client(&loop, serverPath);
    
    // Connect to server
    client.connect();
    
    std::cout << "Client started. Press Ctrl+C to stop." << std::endl;
    loop.loop();
    
    return 0;
}
