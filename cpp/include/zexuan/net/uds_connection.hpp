#ifndef ZEXUAN_NET_UDS_CONNECTION_HPP
#define ZEXUAN_NET_UDS_CONNECTION_HPP

#include <any>
#include <memory>
#include <string>
#include <string_view>

#include "zexuan/net/buffer.hpp"
#include "zexuan/net/callbacks.hpp"
#include "zexuan/net/channel.hpp"
#include "zexuan/net/socket.hpp"

namespace zexuan {
  namespace net {

    class Channel;
    class EventLoop;
    class Socket;

    ///
    /// UDS connection, for both client and server usage.
    ///
    /// This is an interface class, so don't expose too much details.
    class UdsConnection : public std::enable_shared_from_this<UdsConnection> {
    public:
      /// Constructs a UdsConnection with a connected sockfd
      ///
      /// User should not create this object.
      UdsConnection(EventLoop* loop, const std::string& name, int sockfd,
                    const std::string& localPath, const std::string& peerPath);
      ~UdsConnection();

      // 禁用拷贝构造和赋值
      UdsConnection(const UdsConnection&) = delete;
      UdsConnection& operator=(const UdsConnection&) = delete;

      EventLoop* getLoop() const noexcept { return loop_; }
      const std::string& name() const noexcept { return name_; }
      const std::string& localPath() const noexcept { return localPath_; }
      const std::string& peerPath() const noexcept { return peerPath_; }
      bool connected() const noexcept { return state_ == kConnected; }
      bool disconnected() const noexcept { return state_ == kDisconnected; }

      // Get file descriptor for unique identification
      int fd() const noexcept { return channel_->fd(); }

      void send(const void* message, int len);
      void send(const std::string_view& message);
      void send(Buffer* message);  // this one will swap data
      void shutdown();             // NOT thread safe, no simultaneous calling
      void forceClose();
      void forceCloseWithDelay(double seconds);
      
      // reading or not
      void startRead();
      void stopRead();
      bool isReading() const noexcept {
        return reading_;
      };  // NOT thread safe, may race with start/stopReadInLoop

      void setContext(const std::any& context) { context_ = context; }

      const std::any& getContext() const noexcept { return context_; }

      std::any* getMutableContext() noexcept { return &context_; }

      void setConnectionCallback(const UdsConnectionCallback& cb) { connectionCallback_ = cb; }

      void setMessageCallback(const UdsMessageCallback& cb) { messageCallback_ = cb; }

      void setWriteCompleteCallback(const UdsWriteCompleteCallback& cb) {
        writeCompleteCallback_ = cb;
      }

      void setHighWaterMarkCallback(const UdsHighWaterMarkCallback& cb, size_t highWaterMark) {
        highWaterMarkCallback_ = cb;
        highWaterMark_ = highWaterMark;
      }

      /// Advanced interface
      Buffer* inputBuffer() noexcept { return &inputBuffer_; }

      Buffer* outputBuffer() noexcept { return &outputBuffer_; }

      /// Internal use only.
      void setCloseCallback(const UdsCloseCallback& cb) { closeCallback_ = cb; }

      // called when UdsServer accepts a new connection
      void connectEstablished();  // should be called only once
      // called when UdsServer has removed me from its map
      void connectDestroyed();  // should be called only once

    private:
      enum StateE { kDisconnected, kConnecting, kConnected, kDisconnecting };
      void handleRead(Timestamp receiveTime);
      void handleWrite();
      void handleClose();
      void handleError();
      void sendInLoop(const void* message, size_t len);
      void sendInLoop(const std::string_view& message);
      void shutdownInLoop();
      void forceCloseInLoop();
      void setState(StateE s) { state_ = s; }
      const char* stateToString() const;
      void startReadInLoop();
      void stopReadInLoop();

      EventLoop* loop_;
      const std::string name_;
      StateE state_;  // FIXME: use atomic variable
      bool reading_;
      // we don't expose those classes to client.
      std::unique_ptr<zexuan::net::Socket> socket_;
      std::unique_ptr<Channel> channel_;
      const std::string localPath_;
      const std::string peerPath_;
      std::any context_;

      UdsConnectionCallback connectionCallback_;
      UdsMessageCallback messageCallback_;
      UdsWriteCompleteCallback writeCompleteCallback_;
      UdsHighWaterMarkCallback highWaterMarkCallback_;
      UdsCloseCallback closeCallback_;
      size_t highWaterMark_;

      Buffer inputBuffer_;
      Buffer outputBuffer_;
    };

    typedef std::shared_ptr<UdsConnection> UdsConnectionPtr;

  }  // namespace net
}  // namespace zexuan

#endif  // ZEXUAN_NET_UDS_CONNECTION_HPP
