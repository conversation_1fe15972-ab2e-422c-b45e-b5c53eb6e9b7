
#ifndef DEVICEOBJ_H
#define DEVICEOBJ_H

/*
 * *************************************************
 *  状态通知流程：
 * 1、前置到设备
 * 前置设备连接通知程序在建立连接成功发送一个空包通知
 * push103收到空包或数据后更新设备中前置设备状态 m_linkFntSta = true
 * push103 监测到连接断开后更新设备中前置设备状态 m_linkFntSta = false
 * 
 * 
 * 2、设备到前置
 * push103收到空包(连接注册）立即回复当前站端设备状态
 * 当站端设备状态发生改变时向前置发送状态通知
 * *************************************************
 */

#include "ZxGlobal_Def.h"
#include "ZxLogFile.h"
#include "ZxLockableObject.h"
#include "ZxLib.h"

#include <list>
#include <string>



typedef enum  _LinkStatus
{
    CONNECT_CREATE = 0,
    CONNECT_CLOSE= 1,
    CONNECT_PROXY = 2
} LinkStatus;


class DeviceObj {
public:
    DeviceObj(int nChNo,std::string name,int nProType);
    virtual ~DeviceObj();
    
public:
    int getChNo() { return m_nChNo;}
    int getProType(){ return m_nProType;}
    std::string getName() { return m_name; }
    
    time_t getStnTim(){return m_tmStn;}
    void resetStnTim() {time(&m_tmStn);}         // 重置站端连接监测时间
    
    void setStnSta(LinkStatus sta);              // 设置当前站端的连接状态
    LinkStatus getStnSta();                      // 获取当前站端的连接状态
    
    void setFntSta(bool sta);                    // 设置当前站端的连接状态
    bool getFntSta();                            // 获取当前站端的连接状态
    
    void addStnMsg(const std::string &sMsg);     // 向站端缓存添加数据
    bool getStnMsg(std::string &sMsg);           // 只获取
    bool getStnMsgEx(std::string &sMsg);         // 获取后删除
    
    void addFntMsg(const std::string &sMsg);     // 向前置缓存添加数据
    bool getFntMsg(std::string &sMsg);           // 只获取
    bool getFntMsgEx(std::string &sMsg);         // 获取后删除
    
    void clearFntMsg();                          // 清除来自前置数据缓存
    void clearStnMsg();                          // 清除来自子站的数据缓存
    
    int getFntListSize();                       // 获取前置缓存数据个数 
    int getStnListSize();                       // 获取站端缓存数据个数
    bool getStaNotify();                        // 获取连接状态通知对象
    
private:
    
    int m_nChNo;
    std::string m_name;
    int m_nProType;
 
    time_t m_tmStn;         // 用于子站的连接超时
    bool m_bStaNotifyFtn;   // 连接状态是否通知前置
    
    CXJLock m_lockFntDt;    // 前置数据锁
    CXJLock m_lockStnDt;    // 子站数据锁
    CXJLock m_lockStnSta;   // 子站状态锁
    CXJLock m_lockFntSta;   // 前置状态锁
    
    LinkStatus m_linkStnSta;   // 站端设备状态
    bool       m_bLinkFntSta;  // 前置连接状态
    std::list<std::string> m_lstFntMsg;   // 前置过来数据缓存
    std::list<std::string> m_lstStnMsg;  //  子站过来数据缓存
};

#endif /* DEVICEOBJ_H */

