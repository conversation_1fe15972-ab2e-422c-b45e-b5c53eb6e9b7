# Zcs104VlanSrv 技术文档

## 1. 项目概述

### 1.1 项目简介
Zcs104VlanSrv 是一个基于 IEC 104 和 IEC 103 协议的电力系统通信服务程序，主要用于电力调度系统中的厂站通信和数据透传。该程序支持多种协议转换、加密通信、MMS 协议解析等功能。

### 1.2 版本信息
- **当前版本**: 1.0.39 (2025-07-22)
- **编译平台**: Linux (支持 AIX、SUN、HP 等多平台)
- **开发语言**: C/C++
- **编译器**: GCC/G++

### 1.3 主要功能特性
- IEC 104 协议透传和处理
- IEC 103 协议支持
- MMS 协议解析和转换
- SM2 国密加密算法支持
- 多厂站并发通信管理
- 实时数据透传和缓存
- 设备状态监控和管理
- 日志记录和调试功能

## 2. 系统架构

### 2.1 整体架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    Zcs104VlanSrv 主程序                      │
├─────────────────────────────────────────────────────────────┤
│  Main.cpp (程序入口)                                         │
│  ├── 信号处理 (Signal Handler)                               │
│  ├── 服务管理 (Service Management)                           │
│  └── 进程管理 (Process Management)                           │
├─────────────────────────────────────────────────────────────┤
│  TaskMngr (任务管理器)                                        │
│  ├── 配置管理 (Configuration Management)                     │
│  ├── 线程管理 (Thread Management)                            │
│  ├── 厂站连接管理 (Station Connection Management)            │
│  └── 消息总线接口 (Message Bus Interface)                    │
├─────────────────────────────────────────────────────────────┤
│  协议处理层 (Protocol Layer)                                 │
│  ├── IEC 104 APCI Handler                                   │
│  ├── IEC 103 ASDU Handler                                   │
│  ├── MMS 协议解析器                                          │
│  └── SM2 加密模块                                            │
├─────────────────────────────────────────────────────────────┤
│  数据管理层 (Data Management Layer)                          │
│  ├── DeviceObj (设备对象管理)                                │
│  ├── DevFlowManage (设备流量管理)                            │
│  ├── Push/Push103 (数据推送模块)                             │
│  └── Msg103 (103消息管理)                                   │
├─────────────────────────────────────────────────────────────┤
│  网络通信层 (Network Layer)                                  │
│  ├── TCP Socket 管理                                        │
│  ├── 连接状态监控                                            │
│  └── 数据收发缓存                                            │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件关系图

```mermaid
graph TB
    A[Main.cpp] --> B[TaskMngr]
    B --> C[IEC104 Handler]
    B --> D[IEC103 Handler]
    B --> E[MMS Analyzer]
    B --> F[Push Module]
    B --> G[DeviceObj]

    C --> H[APCI Layer]
    D --> I[ASDU Layer]
    E --> J[Protocol Parser]

    F --> K[Push102]
    F --> L[Push103]

    G --> M[Device Status]
    G --> N[Message Cache]

    H --> O[Network Socket]
    I --> O
    J --> O
```

## 3. 目录结构分析

### 3.1 源代码文件结构

```
Zcs104VlanSrv/
├── Main.cpp                    # 程序主入口
├── TaskMngr.cpp/.h            # 任务管理器核心
├── DeviceObj.cpp/.h           # 设备对象管理
├── DevFlowManage.cpp/.h       # 设备流量管理
├── SecDevFlowModule.cpp/.h    # 安全设备流量模块
├── push.cpp/.h                # 数据推送模块(102)
├── push103.cpp/.h             # 数据推送模块(103)
├── Msg103.cpp/.h              # 103消息管理
├── APCI/                      # APCI协议处理目录
│   ├── common/                # 通用APCI组件
│   └── pro/                   # 专用协议处理
│       ├── ZcsHn104VlanCliPro/    # 104客户端协议
│       ├── ZcsHn104VlanFlow/      # 104流量处理
│       └── ZcsProApciHdl/         # APCI处理器
├── MMS/                       # MMS协议处理
│   ├── MMSAnalyze.cpp/.h      # MMS解析器
│   └── MMSdef.h               # MMS定义
├── SM2/                       # SM2加密模块
│   ├── sm2_create_key_pair.c/.h   # 密钥对生成
│   ├── sm2_sign_and_verify.c/.h   # 签名验证
│   └── sm3_with_preprocess.c/.h   # SM3哈希
├── Zcs104VlanSrv.ini          # 主配置文件
├── Makefile                   # 编译配置
└── README.md                  # 技术文档
```

### 3.2 配置文件结构

```
配置文件系统/
├── Zcs104VlanSrv.ini          # 主配置文件
├── Ch2PtId.ini                # 通道到点表映射
├── bus_def.ini                # 总线定义配置
└── ZcsServer.ini              # 服务器配置
```

## 4. 核心组件详解

### 4.1 Main.cpp - 程序入口

**主要功能**:
- 程序启动和初始化
- 信号处理和优雅退出
- 服务模式和守护进程管理
- 多进程管理（主进程-子进程模式）

**关键函数**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/Main.cpp" mode="EXCERPT">
````cpp
int main(int argc ,char * argv[])
{
    // 信号处理器初始化
    Signale_Handle();

    // Linux下支持守护进程模式和单站模式
    if( 0x00 == nOptFlag ){
        // 守护进程模式 - 管理多个厂站子进程
        init_daemon();
        hCmpTaskMngr.Init_daemon();
        while(!bExit){
            // 监控和重启子进程
            for(map<string,STN_CFG>::iterator it=hCmpTaskMngr.mapStnLinkCfg.begin();
                it!=hCmpTaskMngr.mapStnLinkCfg.end();it++){
                int nNowPid = it->second.nPid;
                it->second.nPid = start_child(nNowPid,hCmpTaskMngr.m_strCmdName.c_str(),
                                             "-s",it->first.c_str());
            }
            sleep(5);
        }
    }else if( 0x01 == nOptFlag ){
        // 单站模式 - 处理指定厂站
        KServiceMain(0,NULL);
    }
}
````
</augment_code_snippet>

**信号处理机制**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/Main.cpp" mode="EXCERPT">
````cpp
void sigroutine(int nsig)
{
    // 记录信号接收日志
    hCmpTaskMngr.m_pLogFile->FormatAdd(CLogFile::trace,
        "the process of PID=%d recv a signal of value=%d",getpid(),nsig);

    // 设置退出标志
    bExit = true;
}

void Signale_Handle()
{
    signal(SIGINT,sigroutine);   // Ctrl+C
    signal(SIGTERM,sigroutine);  // 终止信号
    signal(SIGQUIT,sigroutine);  // 退出信号
    signal(SIGPIPE, SIG_IGN);    // 忽略管道破裂
}
````
</augment_code_snippet>

### 4.2 TaskMngr - 任务管理器

**核心职责**:
- 配置文件加载和解析
- 厂站连接管理和监控
- 协议处理器初始化
- 消息总线接口管理
- 线程池管理

**主要数据结构**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/TaskMngr.h" mode="EXCERPT">
````cpp
typedef struct _STN_CFG
{
    int    nNo;                    // 站号
    string strStnId;               // 站ID
    string strMyIP;                // 本地IP
    string strStnIP;               // 厂站IP
    int    nStnPort;               // 厂站端口
    string strStnPubKey;           // 厂站公钥
    string strMyPrivKey;           // 本地私钥
    STN_APCI  sApciPoiner;         // APCI处理器指针
    map<int,string> mapStnDevId;   // 设备ID映射
    int     nPid;                  // 子进程PID
}STN_CFG;

typedef struct _STN_APCI
{
    CXJ104APCIHandler *pAPCIHander;           // 104协议处理器
    CHuNTc103ASDUHandler * pFlowASDUHander;   // 103协议处理器
    CXJPro103ClientWay * pStnFlow;            // 103客户端
    CMessageLog *pFlowLog;                    // 流量日志
    CMessageLog *pMessageLog;                 // 消息日志
}STN_APCI;
````
</augment_code_snippet>

**配置加载流程**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/TaskMngr.cpp" mode="EXCERPT">
````cpp
int CTaskMngr::LoadConfigFile()
{
    // 1. 加载主配置文件
    CXJIniFile iniFile(Zcs104VlanSrvINI);

    // 2. 读取日志配置
    m_LogCfg.nLogLevel = iniFile.GetInt("LOG", "LogLevel", 3);
    m_LogCfg.strLogPath = iniFile.GetString("LOG", "LogRootPath", "../zx_log");

    // 3. 读取网络配置
    sListen61850.nPort = iniFile.GetInt("NET", "ListenPort", 102);
    sListen61850.n103Port = iniFile.GetInt("NET", "Listen103Port", 103);

    // 4. 读取厂站配置
    int total_pair_num = iniFile.GetInt("STN_LINK", "total_pair_num", 0);
    for(int i = 1; i <= total_pair_num; i++){
        STN_CFG stnCfg;
        char szKey[64];
        sprintf(szKey, "StnID%d", i);
        stnCfg.strStnId = iniFile.GetString("STN_LINK", szKey, "");
        // ... 读取其他配置项
        mapStnLinkCfg[stnCfg.strStnId] = stnCfg;
    }
}
````
</augment_code_snippet>

### 4.3 协议处理层

#### 4.3.1 IEC 104 协议处理

**APCI 处理器**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/TaskMngr.cpp" mode="EXCERPT">
````cpp
// 创建104协议处理器
CXJ104APCIHandler *p104APCIHandler = new CXJ104APCIHandler(
    &(*itStnLink->second.sApciPoiner.pMsg104AttachFactory),
    &itStnLink->second.sApciPoiner.Interface,
    &(*itStnLink->second.sApciPoiner.pFlowLog),
    &(*itStnLink->second.sApciPoiner.pMessageLog),
    &(*itStnLink->second.sApciPoiner.pMsgCaster),
    itStnLink->second.strMyPrivKey,     // 私钥
    itStnLink->second.strMyPubKey,      // 公钥
    itStnLink->second.strStnPubKey,     // 厂站公钥
    itStnLink->second.strMyIP,          // 本地IP
    sCtlKeyCfg.nDoSM2);                // SM2加密开关
````
</augment_code_snippet>

#### 4.3.2 IEC 103 协议处理

**ASDU 处理器**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/TaskMngr.cpp" mode="EXCERPT">
````cpp
// 创建103协议处理器
CHuNTc103ASDUHandler * pFlowASDUHander = new CHuNTc103ASDUHandler(
    &itStnLink->second.sApciPoiner.Interface,
    &(*itStnLink->second.sApciPoiner.pFlowLog),
    &(*itStnLink->second.sApciPoiner.pMessageLog));

// 创建103客户端
CXJPro103ClientWay * pStnFlow = new CXJPro103ClientWay(
    itStnLink->second.sApciPoiner.Interface,
    *pFlowASDUHander,
    *itStnLink->second.sApciPoiner.pMsg104AttachFactory,
    *itStnLink->second.sApciPoiner.pFlowLog,
    itStnLink->second.sApciPoiner.pMsgCaster);
````
</augment_code_snippet>

#### 4.3.3 MMS 协议解析

**MMS 消息分析**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/TaskMngr.cpp" mode="EXCERPT">
````cpp
// MMS协议透传处理
int nCanTc = HasLastCOTP(itStn->second.sApciPoiner.pStnFlow->m_strStnId,
                        itCh->first,strOneTpkt);
if(nCanTc==1){
    // 检查是否为MMS消息
    if(sCtlKeyCfg.nUseMMS==1){
        Pack_TPKT vByteOneTpkt;
        list_TPKT listTokt;
        vByteOneTpkt.assign(strOneTpkt.begin(),strOneTpkt.end());
        listTokt.push_back(vByteOneTpkt);
        unsigned int nInvokeID = -1;
        bool bMMsOk = itStn->second.sApciPoiner.pStnFlow->m_pCMMSAnalyze->
                     Analyze_InvokeID(listTokt,nInvokeID);
        if(bMMsOk && nInvokeID==0){
            // 删除召唤版本信息包，不允许透传
            continue;
        }
    }
    // 透传到前置
    TransparentToFront(strOneTpkt, itStn->first);
}
````
</augment_code_snippet>

### 4.4 设备对象管理 (DeviceObj)

**设备状态管理**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/DeviceObj.h" mode="EXCERPT">
````cpp
class DeviceObj {
public:
    // 连接状态枚举
    typedef enum  _LinkStatus {
        CONNECT_CREATE = 0,    // 连接建立
        CONNECT_CLOSE= 1,      // 连接关闭
        CONNECT_PROXY = 2      // 代理连接
    } LinkStatus;

    // 核心方法
    void setStnSta(LinkStatus sta);              // 设置站端连接状态
    LinkStatus getStnSta();                      // 获取站端连接状态
    void setFntSta(bool sta);                    // 设置前置连接状态
    bool getFntSta();                            // 获取前置连接状态

    void addStnMsg(const std::string &sMsg);     // 向站端缓存添加数据
    bool getStnMsgEx(std::string &sMsg);         // 获取并删除站端消息
    void addFntMsg(const std::string &sMsg);     // 向前置缓存添加数据
    bool getFntMsgEx(std::string &sMsg);         // 获取并删除前置消息

private:
    LinkStatus m_linkStnSta;                     // 站端设备状态
    bool       m_bLinkFntSta;                    // 前置连接状态
    std::list<std::string> m_lstFntMsg;          // 前置数据缓存
    std::list<std::string> m_lstStnMsg;          // 子站数据缓存
    CXJLock m_lockFntDt;                         // 前置数据锁
    CXJLock m_lockStnDt;                         // 子站数据锁
};
````
</augment_code_snippet>

### 4.5 数据推送模块 (Push/Push103)

**Push 模块架构**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/push.h" mode="EXCERPT">
````cpp
class CLIENT
{
public:
    int sock;                    // Socket句柄
    int type;                    // 连接类型
    #define TP_LOGIN            1    // 登录
    #define TP_RELOGIN          2    // 重新登录
    #define TP_RECR             4    // 接收
    #define TP_DT               5    // 数据传输
    #define TP_REDT             6    // 重传数据
    #define TP_END_CONNECTION   99   // 结束连接

    char rcvBuf[BUF_SZ];        // 接收缓冲区
    char buf[BUF_SZ];           // 发送缓冲区
    int total_Rcv;              // 总接收字节数
    int curr_Rcv;               // 当前接收字节数
    int total_trans;            // 总发送字节数
    int curr_trans;             // 当前发送字节数

    sockaddr_in addr;           // 网络地址
    string strStnId;            // 连接的厂站ID
    time_t tmo;                 // 超时时间
};

class push
{
public:
    push(CMessageLog* pFlowLog, bool * pExit);
    ~push();

    int start(const string& ip, int port, const string& stnId,
             map<string,pthread_t>& threadIds);
    int wnonblock(CLIENT* c);    // 设置非阻塞模式
    int rnonblock(CLIENT* c);    // 读取非阻塞数据
};
````
</augment_code_snippet>

### 4.6 SM2 加密模块

**SM2 数字签名**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/SM2/sm2_sign_and_verify.c" mode="EXCERPT">
````c
int sm2_sign_data(const unsigned char *message,
                  const int message_len,
                  const unsigned char *id,
                  const int id_len,
                  const unsigned char *pub_key,
                  const unsigned char *pri_key,
                  SM2_SIGNATURE_STRUCT *sm2_sig)
{
    unsigned char digest[32];

    // 1. 计算SM3摘要
    if (sm3_digest_with_preprocess(message, message_len, id, id_len,
                                  pub_key, digest)) {
        return error_code;
    }

    // 2. 生成数字签名
    // ... SM2签名算法实现

    return 0;
}
````
</augment_code_snippet>

**SM3 哈希预处理**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/SM2/sm3_with_preprocess.c" mode="EXCERPT">
````c
int sm3_digest_with_preprocess(const unsigned char *message,
                               const int message_len,
                               const unsigned char *id,
                               const int id_len,
                               const unsigned char *pub_key,
                               unsigned char *digest)
{
    unsigned char z_digest[32];
    EVP_MD_CTX *md_ctx;
    const EVP_MD *md;

    // 1. 计算Z值摘要
    if (sm3_digest_z(id, id_len, pub_key, z_digest)) {
        return COMPUTE_SM3_DIGEST_FAIL;
    }

    // 2. 计算最终摘要
    md = EVP_sm3();
    md_ctx = EVP_MD_CTX_new();
    EVP_DigestInit_ex(md_ctx, md, NULL);
    EVP_DigestUpdate(md_ctx, z_digest, 32);
    EVP_DigestUpdate(md_ctx, message, message_len);
    EVP_DigestFinal_ex(md_ctx, digest, NULL);

    EVP_MD_CTX_free(md_ctx);
    return 0;
}
````
</augment_code_snippet>

## 5. 程序流程分析

### 5.1 程序启动流程

```mermaid
sequenceDiagram
    participant Main as Main.cpp
    participant TaskMngr as TaskMngr
    participant Config as 配置管理
    participant Protocol as 协议处理
    participant Network as 网络层

    Main->>Main: 信号处理初始化
    Main->>TaskMngr: 创建任务管理器
    TaskMngr->>Config: 加载配置文件
    Config-->>TaskMngr: 返回配置信息
    TaskMngr->>Protocol: 初始化协议处理器
    Protocol-->>TaskMngr: 协议处理器就绪
    TaskMngr->>Network: 启动网络监听
    Network-->>TaskMngr: 网络服务启动
    TaskMngr->>TaskMngr: 启动工作线程
    Main->>Main: 进入主循环
```

### 5.2 数据处理流程

```mermaid
flowchart TD
    A[接收网络数据] --> B{数据类型判断}
    B -->|IEC 104| C[104协议解析]
    B -->|IEC 103| D[103协议解析]
    B -->|MMS| E[MMS协议解析]

    C --> F[APCI处理]
    D --> G[ASDU处理]
    E --> H[MMS分析]

    F --> I{是否需要加密}
    G --> I
    H --> I

    I -->|是| J[SM2加密处理]
    I -->|否| K[直接透传]

    J --> L[数据缓存]
    K --> L

    L --> M[推送到目标]
    M --> N[发送到网络]
```

### 5.3 多进程管理流程

**守护进程模式**:
1. **主进程**: 负责监控和管理所有厂站子进程
2. **子进程**: 每个厂站对应一个独立的子进程
3. **进程监控**: 主进程定期检查子进程状态，异常时自动重启

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/Main.cpp" mode="EXCERPT">
````cpp
// 主进程监控循环
while(!bExit){
    // 重新加载配置文件
    if(time(NULL)-checktime>60){
        hCmpTaskMngr.ReLoadConfigFile();
        checktime = time(NULL);
    }

    // 检查并重启子进程
    for(map<string,STN_CFG>::iterator it=hCmpTaskMngr.mapStnLinkCfg.begin();
        it!=hCmpTaskMngr.mapStnLinkCfg.end();it++){
        int nNowPid = it->second.nPid;
        it->second.nPid = start_child(nNowPid, hCmpTaskMngr.m_strCmdName.c_str(),
                                     "-s", it->first.c_str());
        if(it->second.nPid != nNowPid){
            hCmpTaskMngr.m_pLogFile->FormatAdd(CLogFile::trace,
                "重启厂站进程 [%s] pid[%d]->[%d]",
                it->first.c_str(), nNowPid, it->second.nPid);
        }
    }
    sleep(5);
}
````
</augment_code_snippet>

## 6. 配置文件详解

### 6.1 主配置文件 (Zcs104VlanSrv.ini)

**日志配置**:
```ini
[LOG]
LogDays=10                    # 日志保存天数
LogLevel=3                    # 日志级别(0-4)
LogRootPath=/home/<USER>/var/zexin/Zx_Log/  # 日志根路径
LogMaxSize=1000000           # 日志文件最大大小(KB)
ShowDebugLog=0               # 是否显示调试日志
```

**控制开关**:
```ini
[CTL_KEY]
DoSM2Chk=0                   # SM2加密验证开关
DoCrcChk=0                   # CRC校验开关
UseMMSanalyze=0              # MMS协议解析开关
ModelFileCallType=0          # 模型文件调用类型
```

**网络配置**:
```ini
[NET]
ListenPort=102               # 102协议监听端口
Listen103Port=103            # 103协议监听端口

# 绑定IP配置
BindIpNum=2                  # 绑定IP数量
BindIp1=*************       # 绑定IP1
StnId1=厂站A                 # IP1对应的厂站ID
BindIp2=*************       # 绑定IP2
StnId2=厂站B                 # IP2对应的厂站ID
```

**厂站连接配置**:
```ini
[STN_LINK]
total_pair_num=4             # 厂站连接对数
104failCount=5               # 104通信连续失败次数阈值

# 厂站1配置
StnID1=厂站1                 # 厂站ID
MyIp1=***********           # 本地IP
StnIp1=***********          # 厂站IP
StnPort1=2404               # 厂站端口
SM2StnPubFile1=/path/to/pub2.pem      # 厂站公钥文件
SM2MyPubFile1=/path/to/pub1.pem       # 本地公钥文件
SM2MyPrvFile1=/path/to/priv1.pem      # 本地私钥文件
StnConnectChgSec1=60        # 厂站通信状态变化检测时间(秒)
```

### 6.2 编译配置 (Makefile)

**编译选项**:
```makefile
APPNAME=Zcs104VlanSrv        # 应用程序名称
VER=1.0.39                   # 版本号
PLATNAME=OS_LINUX            # 目标平台

# 调试/发布模式
_D=Y                         # Y=调试模式, N=发布模式
RUN_FLAGS = -g -D_DEBUG      # 调试标志
CHAR_FLAGS = -finput-charset=GB18030 -fexec-charset=GB18030

# 依赖库
ZXCOMMON_LIBS = -lzxcommon   # 通用库
OPENSSL_LIBS = -lcrypto      # OpenSSL加密库
LIBS = -lpthread -ldl -lrt   # 系统库
```

## 7. 接口和API说明

### 7.1 消息总线接口

**STTP消息处理**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/TaskMngr.cpp" mode="EXCERPT">
````cpp
int CTaskMngr::_sendSttp2Msgbus(const STTP_FULL_DATA& SttpData)
{
    if(m_pIZxBusSwap == NULL){
        return -1;
    }

    // 发送STTP消息到消息总线
    int nRet = m_pIZxBusSwap->SendSttpData(SttpData);
    if(nRet != 0){
        m_pLogFile->FormatAdd(CLogFile::error,
            "[CTaskMngr::_sendSttp2Msgbus()] 发送STTP消息失败, ret=%d", nRet);
        return -1;
    }

    return 0;
}
````
</augment_code_snippet>

**消息总线回调**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/TaskMngr.cpp" mode="EXCERPT">
````cpp
int CTaskMngr::_bus_swap_callback(void * pRegObj, STTP_FULL_DATA& sttp_data,
                                 string& strAppNodeName, BUS_ADDITION_INFO & addition_info)
{
    CTaskMngr* pThis = (CTaskMngr*)pRegObj;

    // 处理接收到的STTP消息
    int uMsgID = sttp_data.sttp_head.uMsgID;
    switch(uMsgID)
    {
    case 203:  // 目录
    case 210:  // 文件
    case 213:  // 下装
        {
            string strStnId = sttp_data.sttp_body.ch_pt_id;
            map<string,STN_CFG>::iterator itGet = pThis->mapStnLinkCfg.find(strStnId);
            if(itGet != pThis->mapStnLinkCfg.end()){
                // 将消息转发到对应厂站
                CLockUp lockUp(&itGet->second.sApciPoiner.pStnFlow->m_LockForRcvBusInfList);
                itGet->second.sApciPoiner.pStnFlow->m_RcvBusInfList.push_back(sttp_data);
            }
        }
        break;
    }

    return 0;
}
````
</augment_code_snippet>

### 7.2 数据库接口

**厂站配置查询**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/TaskMngr.h" mode="EXCERPT">
````cpp
class CTaskMngr
{
private:
    CXJDBFacade *m_pDBAcess;     // 数据库访问对象

public:
    // 从数据库获取日志设置
    bool GetLogSetFromDB();

    // 从数据库获取厂站管理点表ID
    int GetStnMngrPtIdFromDB();

    // 判断是否为厂站管理器
    bool IsStnMgr(string strPtId, string &strStn);
};
````
</augment_code_snippet>

### 7.3 服务在线管理接口

**服务器在线管理**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/TaskMngr.h" mode="EXCERPT">
````cpp
class CTaskMngr
{
private:
    CXJSrvOnlineManager* m_pSrvOnlineMngr;    // 服务器在线管理器指针
    bool m_bSrvOnlineMngrEnabled;             // 服务器在线管理器是否启用

public:
    // 初始化服务器在线管理器
    int InitSrvOnlineManager();

    // 从数据库获取服务器在线管理器配置
    bool GetSrvOnlineMngrConfigFromDB();

    // 启动厂站进程
    int StartStationProcess(const std::string& stationId);

    // 停止厂站进程
    int StopStationProcess(const std::string& stationId);

    // 更新厂站通信状态
    void UpdateStationCommStatus(const std::string& stationId, int newStatus);

    // 服务器切换回调
    static int _srv_switch_callback(void* pParam, int pStatus,
                                   std::vector<stXJSubstation>& pStationList);

    // 处理服务器切换
    int OnServerSwitch(int pStatus, std::vector<stXJSubstation>& pStationList);
};
````
</augment_code_snippet>

## 8. 错误处理和日志系统

### 8.1 日志系统架构

**日志级别定义**:
- **0**: 不记录日志
- **1**: 错误级别 (error)
- **2**: 警告级别 (warning)
- **3**: 跟踪级别 (trace)
- **4**: 调试级别 (debug)

**日志记录示例**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/TaskMngr.cpp" mode="EXCERPT">
````cpp
// 格式化日志记录
m_pLogFile->FormatAdd(CLogFile::trace,
    "[CTaskMngr::DoTaskManager()] 厂站[%s] 处理消息ID[%d]",
    strStnId.c_str(), uMsgID);

// 错误日志记录
m_pLogFile->FormatAdd(CLogFile::error,
    "[CTaskMngr::Start()] 厂站[%s] 创建104协议处理器失败",
    itStnLink->first.c_str());

// 调试日志记录
if(m_LogCfg.bShowDebug){
    m_pLogFile->FormatAdd(CLogFile::debug,
        "[CTaskMngr::DoTaskManager()] 调试信息: 数据长度=%d",
        dataLen);
}
````
</augment_code_snippet>

### 8.2 异常处理机制

**信号异常处理**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/Main.cpp" mode="EXCERPT">
````cpp
void dump(int nsig)
{
    char buf[1024]="";
    char cmd[1024]="";
    FILE * fh = NULL;

    // 记录异常信号
    hCmpTaskMngr.m_pLogFile->FormatAdd(CLogFile::trace,
        "进程PID=%d 接收到异常信号=%d", getpid(), nsig);

    // 获取启动命令行
    sprintf(buf,"/proc/%d/cmdline", getpid());
    if((fh = fopen(buf,"r"))){
        if(fgets(buf, sizeof(buf), fh)){
            fclose(fh);
            // 调用gdb调试
            sprintf(cmd,"gdb %s %d", buf, getpid());
            system(cmd);
        }
    }

    // 设置退出标志
    bExit = true;
}
````
</augment_code_snippet>

**网络异常处理**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/push.h" mode="EXCERPT">
````cpp
// 网络错误检查宏
#define _CHECK_BLOCK    if(r == 0)return -1;\
                        if(r <  0){if (errno ==  EWOULDBLOCK)return 0; else return -1;}

// 非阻塞Socket处理
int push::wnonblock(CLIENT* c)
{
    int r = send(c->sock, c->buf + c->curr_trans,
                c->total_trans - c->curr_trans, 0);
    _CHECK_BLOCK;

    c->curr_trans += r;
    if(c->curr_trans >= c->total_trans){
        // 发送完成
        return 1;
    }
    return 0;  // 需要继续发送
}
````
</augment_code_snippet>

## 9. 性能优化和监控

### 9.1 内存管理

**缓冲区管理**:
- 接收缓冲区: 1MB (BUF_SZ = 1*1024*1024)
- 发送缓冲区: 1MB
- 消息缓存: 动态分配，支持大容量MMS消息

**内存池优化**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/DeviceObj.h" mode="EXCERPT">
````cpp
class DeviceObj {
private:
    std::list<std::string> m_lstFntMsg;   // 前置消息缓存
    std::list<std::string> m_lstStnMsg;   // 子站消息缓存

public:
    void clearFntMsg();                   // 清除前置数据缓存
    void clearStnMsg();                   // 清除子站数据缓存
    int getFntListSize();                 // 获取前置缓存数据个数
    int getStnListSize();                 // 获取站端缓存数据个数
};
````
</augment_code_snippet>

### 9.2 线程管理

**线程池架构**:

<augment_code_snippet path="dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv/TaskMngr.h" mode="EXCERPT">
````cpp
class CTaskMngr
{
private:
    // 线程管理
    map<int,THREAD_INFO> m_map104StnThreadInfo;     // 104厂站线程信息
    THREAD_HANDLE m_hTheadHandeTaskMngr;            // 任务管理器线程句柄
    THREAD_ID m_hTheadIdTaskMngr;                   // 任务管理器线程ID

    // 推送线程管理
    map<string,map<string,pthread_t>> map102PushTheadId;  // 102推送线程ID
    map<string,map<string,pthread_t>> map103PushTheadId;  // 103推送线程ID

public:
    // 线程处理函数
    static THREAD_FUNC _TaskMngrThread(void *arg);      // 任务管理器线程
    static THREAD_FUNC _Task104StnThread(void *arg);    // 104厂站线程
};
````
</augment_code_snippet>

### 9.3 性能监控指标

**通信状态监控**:
- 厂站连接状态
- 数据传输速率
- 错误计数器
- 超时检测

**系统资源监控**:
- CPU使用率
- 内存使用情况
- 网络连接数
- 线程状态

## 10. 部署和运维

### 10.1 编译部署

**编译步骤**:
```bash
# 1. 进入源码目录
cd /root/zexuan/dragon/module/ZcsServer/pro/Zcs104VlanSrv/Zcs104VlanSrv

# 2. 编译程序
make clean
make

# 3. 编译结果
# 调试版本: ../../../../../bin/debug/ZcsHnTcFrontSrv/Zcs104VlanSrv
# 发布版本: ../../../../../bin/release/ZcsHnTcFrontSrv/Zcs104VlanSrv-1.0.39
```

**依赖库**:
- libzxcommon: 通用功能库
- libcrypto: OpenSSL加密库 (SM2/SM3支持)
- libpthread: POSIX线程库
- libdl: 动态链接库
- librt: 实时扩展库

### 10.2 运行模式

**守护进程模式** (推荐生产环境):
```bash
# 启动守护进程，管理所有厂站
./Zcs104VlanSrv

# 查看运行状态
ps -ef | grep Zcs104VlanSrv
```

**单厂站模式** (调试和测试):
```bash
# 启动指定厂站服务
./Zcs104VlanSrv -s 厂站ID

# 查看帮助信息
./Zcs104VlanSrv -h
```

### 10.3 配置管理

**配置文件检查**:
1. 确保 `Zcs104VlanSrv.ini` 配置正确
2. 检查厂站IP和端口配置
3. 验证SM2密钥文件路径
4. 确认日志目录权限

**运行时配置重载**:
- 守护进程模式下，每60秒自动重载配置
- 支持动态添加/删除厂站配置
- 配置变更后自动重启相关子进程

### 10.4 故障排查

**常见问题**:

1. **厂站连接失败**
   - 检查网络连通性
   - 验证IP和端口配置
   - 查看防火墙设置

2. **SM2加密错误**
   - 检查密钥文件路径和权限
   - 验证密钥文件格式(PEM)
   - 确认OpenSSL版本兼容性

3. **内存泄漏**
   - 监控进程内存使用
   - 检查消息缓存清理
   - 分析日志中的异常信息

**日志分析**:
```bash
# 查看错误日志
grep "error" /home/<USER>/var/zexin/Zx_Log/Zcs104VlanSrv/*.log

# 监控实时日志
tail -f /home/<USER>/var/zexin/Zx_Log/Zcs104VlanSrv/trace.log

# 分析通信状态
grep "通信状态" /home/<USER>/var/zexin/Zx_Log/Zcs104VlanSrv/*.log
```

## 11. 版本历史和更新

### 11.1 最近版本更新

**Ver 1.0.39 (2025-07-22)**:
- 修复厂站通信中断后发送20002消息的设备通信状态问题
- 优化遥控应答通信状态处理逻辑
- 改进私网透传通信状态管理

**Ver 1.0.38 (2025-07-18)**:
- 新增厂站104通信连续失败次数配置
- 新增厂站通信状态变化检测时间配置
- 优化厂站断开和前置连接处理逻辑
- 改进102链路状态处理

**Ver 1.0.37 (2025-07-07)**:
- 修复厂站透传连接恢复后状态自动恢复问题
- 优化104链路状态判断和数据传输
- 改进STTP消息总线断开时的处理

### 11.2 功能演进

**协议支持演进**:
- 初期: 基础IEC 104协议支持
- 中期: 增加IEC 103协议和MMS解析
- 现在: 完整的多协议转换和加密支持

**性能优化历程**:
- 单线程 → 多线程架构
- 同步通信 → 异步非阻塞通信
- 单进程 → 多进程管理模式

## 12. 扩展和定制

### 12.1 协议扩展

**新增协议支持**:
1. 在APCI目录下创建新的协议处理模块
2. 实现协议解析和转换接口
3. 在TaskMngr中注册新的协议处理器
4. 更新配置文件支持新协议参数

### 12.2 功能定制

**自定义消息处理**:
- 扩展STTP消息类型处理
- 增加特定厂站的协议适配
- 定制加密算法和安全策略

**性能调优**:
- 调整缓冲区大小
- 优化线程池配置
- 定制内存管理策略

---

## 附录

### A. 配置文件模板

详细的配置文件模板和说明请参考 `Zcs104VlanSrv.ini` 文件。

### B. 错误代码对照表

| 错误代码 | 含义 | 处理建议 |
|---------|------|----------|
| -1 | 一般错误 | 检查日志详细信息 |
| -2 | 网络连接失败 | 检查网络配置 |
| -3 | 协议解析错误 | 验证数据格式 |
| -4 | 加密验证失败 | 检查密钥配置 |

### C. 性能基准

**推荐硬件配置**:
- CPU: 4核心以上
- 内存: 8GB以上
- 网络: 千兆以太网
- 存储: SSD硬盘

**性能指标**:
- 并发厂站数: 100+
- 消息处理速率: 10000条/秒
- 响应时间: <10ms
- 可用性: 99.9%

---

*本文档基于 Zcs104VlanSrv v1.0.39 版本编写，如有疑问请联系开发团队。*