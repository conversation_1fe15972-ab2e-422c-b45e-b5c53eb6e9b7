#include <iostream>
#include <unistd.h>
#include <thread>
#include <chrono>

#include "zexuan/net/event_loop.hpp"
#include "zexuan/net/uds_client.hpp"
#include "zexuan/base/logging.hpp"

using namespace zexuan;
using namespace zexuan::net;

class EchoClient {
public:
    EchoClient(EventLoop* loop, const std::string& serverPath)
        : client_(loop, serverPath, "EchoClient"),
          messageCount_(0) {
        client_.setConnectionCallback([this](const UdsConnectionPtr& conn) {
            onConnection(conn);
        });
        client_.setMessageCallback([this](const UdsConnectionPtr& conn, Buffer* buf, Timestamp time) {
            onMessage(conn, buf, time);
        });
        // Enable retry for automatic reconnection
        client_.enableRetry();
    }

    void connect() {
        client_.connect();
    }

    void disconnect() {
        client_.disconnect();
    }

private:
    void onConnection(const UdsConnectionPtr& conn) {
        std::cout << "EchoClient - " << conn->localPath() << " -> " 
                  << conn->peerPath() << " is " 
                  << (conn->connected() ? "UP" : "DOWN") << std::endl;
        
        if (conn->connected()) {
            // Send a test message
            sendMessage(conn);
        }
    }

    void onMessage(const UdsConnectionPtr& conn, Buffer* buf, Timestamp time) {
        std::string msg(buf->retrieveAllAsString());
        std::cout << "EchoClient - received echo: \"" << msg << "\" at " 
                  << std::chrono::duration_cast<std::chrono::milliseconds>(
                       time.time_since_epoch()).count() << std::endl;
        
        messageCount_++;
        
        // Send another message after a delay
        if (messageCount_ < 5) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            sendMessage(conn);
        } else {
            std::cout << "EchoClient - Sent " << messageCount_ << " messages. Disconnecting..." << std::endl;
            conn->shutdown();
        }
    }

    void sendMessage(const UdsConnectionPtr& conn) {
        std::string message = "Hello from UDS client #" + std::to_string(messageCount_ + 1);
        std::cout << "EchoClient - sending: \"" << message << "\"" << std::endl;
        conn->send(message);
    }

    UdsClient client_;
    int messageCount_;
};

int main(int argc, char* argv[]) {
    std::cout << "pid = " << getpid() << std::endl;
    
    // Initialize logging
    zexuan::base::Logger::setLogLevel(zexuan::base::Logger::INFO);
    
    std::string serverPath = "/tmp/echo_server.sock";
    if (argc > 1) {
        serverPath = argv[1];
    }
    
    std::cout << "UDS Echo Client connecting to: " << serverPath << std::endl;
    
    EventLoop loop;
    EchoClient client(&loop, serverPath);
    
    // Connect to server
    client.connect();
    
    std::cout << "Client started. Press Ctrl+C to stop." << std::endl;
    loop.loop();
    
    return 0;
}
