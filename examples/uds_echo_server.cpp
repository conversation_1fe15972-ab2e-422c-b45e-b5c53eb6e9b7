#include <iostream>
#include <unistd.h>

#include "zexuan/net/event_loop.hpp"
#include "zexuan/net/uds_server.hpp"
#include "zexuan/base/logging.hpp"

using namespace zexuan;
using namespace zexuan::net;

class EchoServer {
public:
    EchoServer(EventLoop* loop, const std::string& listenPath)
        : server_(loop, listenPath, "EchoServer") {
        server_.setConnectionCallback([this](const UdsConnectionPtr& conn) {
            onConnection(conn);
        });
        server_.setMessageCallback([this](const UdsConnectionPtr& conn, Buffer* buf, Timestamp time) {
            onMessage(conn, buf, time);
        });
    }

    void start() {
        server_.start();
    }

private:
    void onConnection(const UdsConnectionPtr& conn) {
        std::cout << "EchoServer - " << conn->peerPath() << " -> " 
                  << conn->localPath() << " is " 
                  << (conn->connected() ? "UP" : "DOWN") << std::endl;
    }

    void onMessage(const UdsConnectionPtr& conn, Buffer* buf, Timestamp time) {
        std::string msg(buf->retrieveAllAsString());
        std::cout << "EchoServer - received " << msg.size() << " bytes from " 
                  << conn->peerPath() << " at " 
                  << std::chrono::duration_cast<std::chrono::milliseconds>(
                       time.time_since_epoch()).count() << std::endl;
        
        // Echo back the message
        conn->send(msg);
    }

    UdsServer server_;
};

int main(int argc, char* argv[]) {
    std::cout << "pid = " << getpid() << std::endl;
    
    // Initialize logging
    zexuan::base::Logger::setLogLevel(zexuan::base::Logger::INFO);
    
    std::string listenPath = "/tmp/echo_server.sock";
    if (argc > 1) {
        listenPath = argv[1];
    }
    
    std::cout << "UDS Echo Server listening on: " << listenPath << std::endl;
    
    // Clean up any existing socket file
    unlink(listenPath.c_str());
    
    EventLoop loop;
    EchoServer server(&loop, listenPath);
    server.start();
    
    std::cout << "Server started. Press Ctrl+C to stop." << std::endl;
    loop.loop();
    
    // Clean up socket file on exit
    unlink(listenPath.c_str());
    
    return 0;
}
